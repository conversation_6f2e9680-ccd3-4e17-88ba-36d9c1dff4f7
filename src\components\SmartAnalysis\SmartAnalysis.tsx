import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Camera, Upload, Scan, Brain, FileText, Video, MapPin, DollarSign, Clock, AlertTriangle, Download, Play, ExternalLink, Shield, TrendingUp, Leaf, Droplets, Sun, Calendar } from 'lucide-react';
import { aiService } from '../../services/aiService';
import { AIAnalysisResult } from '../../types';
import { countries } from '../../data/countries';
import toast from 'react-hot-toast';

const SmartAnalysis: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [symptoms, setSymptoms] = useState('');
  const [cropOrAnimalType, setCropOrAnimalType] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('');
  const [analysisType, setAnalysisType] = useState<'crop' | 'livestock'>('crop');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResult | null>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Image size should be less than 10MB');
        return;
      }
      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAnalysis = async () => {
    if (!selectedImage || !symptoms || !cropOrAnimalType) {
      toast.error('Please upload an image, enter symptoms, and specify crop/animal type');
      return;
    }

    setIsAnalyzing(true);
    try {
      const location = selectedCountry && selectedProvince ? 
        `${selectedProvince}, ${selectedCountry}` : undefined;
      
      const result = await aiService.analyzeImageAndSymptoms(
        selectedImage.split(',')[1], // Remove data URL prefix
        symptoms,
        cropOrAnimalType,
        location
      );
      setAnalysisResult(result);
      toast.success('Analysis completed successfully!');
    } catch (error) {
      toast.error('Analysis failed. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-400 bg-green-500/10';
      case 'medium': return 'text-yellow-400 bg-yellow-500/10';
      case 'high': return 'text-orange-400 bg-orange-500/10';
      case 'critical': return 'text-red-400 bg-red-500/10';
      default: return 'text-gray-400 bg-gray-500/10';
    }
  };

  const selectedCountryData = countries.find(c => c.name === selectedCountry);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="p-6 space-y-8"
    >
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4"
        >
          <Brain className="w-8 h-8 text-white" />
        </motion.div>
        <h1 className="text-3xl font-bold text-white mb-2">Smart Analysis</h1>
        <p className="text-gray-400">AI-powered crop and livestock health diagnosis with global expertise</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* Analysis Type Toggle */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Analysis Type</h3>
            <div className="flex bg-white/5 rounded-xl p-1">
              <button
                onClick={() => setAnalysisType('crop')}
                className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${
                  analysisType === 'crop' ? 'bg-green-500 text-white' : 'text-gray-400 hover:text-white'
                }`}
              >
                🌱 Crops
              </button>
              <button
                onClick={() => setAnalysisType('livestock')}
                className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${
                  analysisType === 'livestock' ? 'bg-blue-500 text-white' : 'text-gray-400 hover:text-white'
                }`}
              >
                🐄 Livestock
              </button>
            </div>
          </div>

          {/* Location Selection */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-blue-400" />
              Location (Optional)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Country</label>
                <select
                  value={selectedCountry}
                  onChange={(e) => {
                    setSelectedCountry(e.target.value);
                    setSelectedProvince('');
                  }}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                >
                  <option value="">Select Country</option>
                  {countries.map(country => (
                    <option key={country.code} value={country.name} className="bg-navy-900">
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Province/State</label>
                <select
                  value={selectedProvince}
                  onChange={(e) => setSelectedProvince(e.target.value)}
                  disabled={!selectedCountry}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500 disabled:opacity-50"
                >
                  <option value="">Select Province</option>
                  {selectedCountryData?.provinces.map(province => (
                    <option key={province.code} value={province.name} className="bg-navy-900">
                      {province.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Image Upload */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Camera className="w-5 h-5 text-blue-400" />
              Upload Image
            </h3>
            
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <div className="border-2 border-dashed border-gray-600 rounded-xl p-8 text-center hover:border-primary-500 transition-colors">
                {selectedImage ? (
                  <div className="space-y-4">
                    <img
                      src={selectedImage}
                      alt="Uploaded"
                      className="max-h-48 mx-auto rounded-lg"
                    />
                    <p className="text-green-400 text-sm">Image uploaded successfully</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-white font-medium">
                        Upload {analysisType === 'crop' ? 'crop' : 'livestock'} image
                      </p>
                      <p className="text-gray-400 text-sm">JPG, PNG, or WebP up to 10MB</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Type Specification */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">
              {analysisType === 'crop' ? 'Crop' : 'Livestock'} Type
            </h3>
            <input
              type="text"
              value={cropOrAnimalType}
              onChange={(e) => setCropOrAnimalType(e.target.value)}
              placeholder={analysisType === 'crop' ? 
                "e.g., Tomato, Maize, Wheat, Rice" : 
                "e.g., Cattle, Chickens, Goats, Pigs"
              }
              className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
            />
          </div>

          {/* Symptoms */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Describe Symptoms</h3>
            <textarea
              value={symptoms}
              onChange={(e) => setSymptoms(e.target.value)}
              placeholder={analysisType === 'crop' ? 
                "Describe what you observe: yellowing leaves, spots, wilting, pest damage, growth issues, etc." :
                "Describe what you observe: unusual behavior, appetite changes, physical symptoms, movement issues, etc."
              }
              rows={4}
              className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500 resize-none"
            />
          </div>

          {/* Analyze Button */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleAnalysis}
            disabled={isAnalyzing || !selectedImage || !symptoms || !cropOrAnimalType}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isAnalyzing ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Scan className="w-5 h-5" />
                </motion.div>
                Analyzing...
              </>
            ) : (
              <>
                <Brain className="w-5 h-5" />
                Analyze with AI
              </>
            )}
          </motion.button>
        </motion.div>

        {/* Results Section */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {analysisResult ? (
            <>
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-white mb-2 flex items-center justify-center gap-2">
                    🧠 Smart AI Analysis Module
                  </h2>
                  <p className="text-gray-300 italic">"One click. One photo. Global intelligence in your hands."</p>
                </div>
              </div>

              {/* Analysis Inputs Summary */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  🧪 1. Analysis Inputs
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Analysis Type:</span>
                      <span className="text-white">{analysisType === 'crop' ? '🌱 Crop' : '🐄 Livestock'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Image Uploaded:</span>
                      <span className="text-green-400">✅ Yes</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Symptoms:</span>
                      <span className="text-white truncate max-w-32">{symptoms}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Country:</span>
                      <span className="text-white">🌍 {selectedCountry || 'Global'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Province/Region:</span>
                      <span className="text-white">{selectedProvince || 'Not specified'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Produce Name:</span>
                      <span className="text-white">{cropOrAnimalType}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Real-Time AI-Powered Output Report */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  📊 2. Real-Time AI-Powered Output Report
                </h3>

                {/* A. Diagnosis & Confidence Score */}
                <div className="mb-6">
                  <h4 className="text-md font-semibold text-white mb-3 flex items-center gap-2">
                    🧬 A. Diagnosis & Confidence Score
                  </h4>
                  <div className="bg-white/5 rounded-xl p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400 block">Diagnosis:</span>
                        <span className="text-white font-semibold">{analysisResult.diagnosis}</span>
                      </div>
                      <div>
                        <span className="text-gray-400 block">Confidence:</span>
                        <span className="text-green-400 font-semibold">{analysisResult.confidence}%</span>
                      </div>
                      <div>
                        <span className="text-gray-400 block">Severity Level:</span>
                        <span className={`font-semibold ${getSeverityColor(analysisResult.severity)}`}>
                          {analysisResult.severity?.charAt(0).toUpperCase() + analysisResult.severity?.slice(1) || 'Unknown'}
                        </span>
                      </div>
                    </div>
                    <div className="mt-3">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-gray-400 text-xs">Confidence Level:</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-green-400 to-blue-400 h-2 rounded-full"
                          style={{ width: `${analysisResult.confidence}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* B. Cost & Time Estimate */}
                <div className="mb-6">
                  <h4 className="text-md font-semibold text-white mb-3 flex items-center gap-2">
                    🧾 B. Cost & Time Estimate
                  </h4>
                  <div className="bg-white/5 rounded-xl p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400 block">Estimated Cost:</span>
                        <span className="text-green-400 font-semibold">{analysisResult.costEstimate || '$20 - $50 USD'}</span>
                      </div>
                      <div>
                        <span className="text-gray-400 block">Estimated Recovery:</span>
                        <span className="text-blue-400 font-semibold">{analysisResult.estimatedRecovery}</span>
                      </div>
                      <div>
                        <span className="text-gray-400 block">Urgency Level:</span>
                        <span className="text-yellow-400 font-semibold">⏳ {analysisResult.severity === 'high' || analysisResult.severity === 'critical' ? 'High' : 'Moderate'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* C. Recommended Treatment */}
                <div className="mb-6">
                  <h4 className="text-md font-semibold text-white mb-3 flex items-center gap-2">
                    💊 C. Recommended Treatment
                  </h4>
                  <div className="bg-white/5 rounded-xl p-4">
                    <p className="text-gray-400 text-sm mb-3">Steps:</p>
                    <ol className="space-y-3">
                      {analysisResult.treatment.map((step, index) => (
                        <li key={index} className="text-gray-300 flex items-start gap-3">
                          <span className="text-blue-400 font-bold bg-blue-500/20 rounded-full w-6 h-6 flex items-center justify-center text-sm">
                            {index + 1}
                          </span>
                          <span className="flex-1">{step}</span>
                        </li>
                      ))}
                    </ol>
                    <div className="mt-4 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                      <p className="text-green-400 text-sm">✅ Treatment source is AI-verified, localized, and practical</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* D. YouTube Video Tutorials */}
              {analysisResult.videoTutorials && analysisResult.videoTutorials.length > 0 && (
                <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                    📹 D. YouTube Video Tutorials (Curated)
                  </h3>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-white/10">
                          <th className="text-left text-gray-400 pb-2">Title</th>
                          <th className="text-left text-gray-400 pb-2">Duration</th>
                          <th className="text-left text-gray-400 pb-2">Relevance</th>
                          <th className="text-left text-gray-400 pb-2">Action</th>
                        </tr>
                      </thead>
                      <tbody className="space-y-2">
                        {analysisResult.videoTutorials.map((video, index) => (
                          <tr key={index} className="border-b border-white/5">
                            <td className="py-3 text-white font-medium">{video.title}</td>
                            <td className="py-3 text-gray-300">{video.duration}</td>
                            <td className="py-3">
                              <div className="flex items-center gap-2">
                                <div className="w-12 bg-gray-700 rounded-full h-1">
                                  <div
                                    className="bg-green-400 h-1 rounded-full"
                                    style={{ width: `${video.relevanceScore}%` }}
                                  />
                                </div>
                                <span className="text-green-400 text-xs">{video.relevanceScore}%</span>
                              </div>
                            </td>
                            <td className="py-3">
                              <button className="flex items-center gap-1 text-red-400 hover:text-red-300 text-sm font-medium">
                                <Play className="w-3 h-3" />
                                ▶️ Watch Now
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                    <p className="text-blue-400 text-sm">Videos fetched using YouTube Data API and filtered by AI for simplicity, region relevance, and clarity (non-technical farmers).</p>
                  </div>
                </div>
              )}

              {/* E. Prevention & Risk Alerts */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  🛡️ E. Prevention & Risk Alerts
                </h3>

                <div className="mb-4">
                  <h4 className="text-md font-semibold text-white mb-3">Prevention Measures:</h4>
                  <ul className="space-y-2">
                    {analysisResult.prevention.map((measure, index) => (
                      <li key={index} className="text-gray-300 flex items-start gap-2">
                        <span className="text-green-400 mt-1">•</span>
                        <span>{measure}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-yellow-400 mb-2">Region-Specific Risk Alerts:</h4>
                  <ul className="space-y-1 text-sm text-gray-300">
                    <li>• Last 6 months: 30% similar cases in {selectedProvince || 'your region'}</li>
                    <li>• Weather forecast may affect condition in next 5 days</li>
                    <li>• Seasonal patterns suggest increased vigilance needed</li>
                  </ul>
                </div>
              </div>

              {/* F. Market Value Analysis */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  💰 F. Market Value Analysis
                </h3>

                <div className="overflow-x-auto mb-4">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-white/10">
                        <th className="text-left text-gray-400 pb-2">Location</th>
                        <th className="text-left text-gray-400 pb-2">Market Price (USD/Kg)</th>
                        <th className="text-left text-gray-400 pb-2">Platform</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-white/5">
                        <td className="py-2 text-white">Harare</td>
                        <td className="py-2 text-green-400 font-semibold">$1.20</td>
                        <td className="py-2 text-blue-400">FarmersMarket.co.zw</td>
                      </tr>
                      <tr className="border-b border-white/5">
                        <td className="py-2 text-white">Bulawayo</td>
                        <td className="py-2 text-green-400 font-semibold">$0.98</td>
                        <td className="py-2 text-blue-400">AgroTradeAfrica.org</td>
                      </tr>
                      <tr className="border-b border-white/5">
                        <td className="py-2 text-white">Chiredzi</td>
                        <td className="py-2 text-green-400 font-semibold">$1.45</td>
                        <td className="py-2 text-blue-400">ZimAgroMarkets</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-green-400 mb-2">Best Recommendation:</h4>
                  <p className="text-gray-300 text-sm">Sell in Chiredzi via ZimAgroMarkets for the highest profit margin this week.</p>
                </div>
              </div>

              {/* G. Value-Addition Tips */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  💼 G. Value-Addition Tips
                </h3>

                <div className="mb-4">
                  <h4 className="text-md font-semibold text-white mb-3">Preservation & Packaging Ideas:</h4>
                  <ul className="space-y-2 text-gray-300">
                    <li className="flex items-start gap-2">
                      <Sun className="w-4 h-4 text-yellow-400 mt-0.5" />
                      <span>Sun-drying with food-grade nets</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Droplets className="w-4 h-4 text-blue-400 mt-0.5" />
                      <span>Basic {cropOrAnimalType.toLowerCase()} paste preparation at home</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Shield className="w-4 h-4 text-green-400 mt-0.5" />
                      <span>Vacuum sealing for longer shelf life</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-purple-400 mb-2">Top Tutorials:</h4>
                  <ul className="space-y-1 text-sm text-gray-300">
                    <li>• Turn {cropOrAnimalType} into Paste for Sale – Simple Video</li>
                    <li>• How to Build Solar Dryers for Farmers – 10 Mins</li>
                  </ul>
                </div>
              </div>
              {/* Bonus AI Tips Section */}
              <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  🧠 Bonus AI Tips Section
                </h3>
                <p className="text-gray-300 italic mb-4">"Because knowledge should never stop"</p>

                <div className="space-y-3 text-sm">
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-blue-400">"Did you know planting cowpeas before maize helps enrich the soil?"</p>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-green-400">"Consider solar irrigation – 40% more yield in dry seasons."</p>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-yellow-400">"AI suggests building a cheap vermicompost pit—watch guide"</p>
                  </div>
                </div>
              </div>

              {/* Data Privacy & Accessibility */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  🔐 Data Privacy & Accessibility
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">End-to-end encrypted uploads</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Brain className="w-4 h-4 text-blue-400" />
                      <span className="text-gray-300">No account needed for quick analysis</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-purple-400" />
                      <span className="text-gray-300">Available in English, Shona, Swahili, and more</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Scan className="w-4 h-4 text-yellow-400" />
                      <span className="text-gray-300">Full-screen UI toggle for mobile users</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-red-400" />
                      <span className="text-gray-300">Works offline with preloaded knowledge cache</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Summary Report Download */}
              <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  ✅ Summary Report Download
                </h3>

                <p className="text-gray-300 mb-4">✔️ Export Report as:</p>

                <div className="flex flex-wrap gap-3">
                  <button className="flex items-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 text-red-400 px-4 py-2 rounded-lg transition-colors">
                    <Download className="w-4 h-4" />
                    PDF (Formatted)
                  </button>
                  <button className="flex items-center gap-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 px-4 py-2 rounded-lg transition-colors">
                    <FileText className="w-4 h-4" />
                    Plain Text
                  </button>
                  <button className="flex items-center gap-2 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 px-4 py-2 rounded-lg transition-colors">
                    <ExternalLink className="w-4 h-4" />
                    Share Link
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-12 text-center">
              <Scan className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Ready for Analysis</h3>
              <p className="text-gray-400">
                Upload an image, describe symptoms, and specify the {analysisType} type to get AI-powered insights with comprehensive reporting
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default SmartAnalysis;