import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Droplets, Sun, AlertCircle, CheckCircle } from 'lucide-react';

const CropTimeline: React.FC = () => {
  const crops = [
    {
      id: 1,
      name: 'Tomatoes',
      variety: 'Roma',
      stage: 'Flowering',
      progress: 65,
      daysToHarvest: 45,
      health: 'healthy',
      nextAction: 'Apply fertilizer',
      image: 'https://images.pexels.com/photos/1327838/pexels-photo-1327838.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      id: 2,
      name: 'Maize',
      variety: 'Sweet Corn',
      stage: 'Vegetative',
      progress: 35,
      daysToHarvest: 85,
      health: 'warning',
      nextAction: 'Pest inspection',
      image: 'https://images.pexels.com/photos/547263/pexels-photo-547263.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      id: 3,
      name: 'Beans',
      variety: 'Green Beans',
      stage: 'Podding',
      progress: 80,
      daysToHarvest: 20,
      health: 'healthy',
      nextAction: 'Prepare for harvest',
      image: 'https://images.pexels.com/photos/1656663/pexels-photo-1656663.jpeg?auto=compress&cs=tinysrgb&w=400'
    }
  ];

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertCircle;
      case 'critical': return AlertCircle;
      default: return CheckCircle;
    }
  };

  return (
    <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white flex items-center gap-2">
          <Calendar className="w-5 h-5 text-primary-400" />
          Crop Timeline
        </h2>
        <button className="text-primary-400 hover:text-primary-300 text-sm font-medium">
          View All
        </button>
      </div>

      <div className="space-y-4">
        {crops.map((crop, index) => {
          const HealthIcon = getHealthIcon(crop.health);
          
          return (
            <motion.div
              key={crop.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="group relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-primary-500/0 to-primary-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              
              <div className="relative bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4 transition-all duration-300 group-hover:border-primary-500/30">
                <div className="flex items-center gap-4">
                  {/* Crop Image */}
                  <div className="w-16 h-16 rounded-xl overflow-hidden bg-gradient-to-br from-primary-400 to-purple-500 flex-shrink-0">
                    <img 
                      src={crop.image} 
                      alt={crop.name}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Crop Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h3 className="text-white font-semibold">{crop.name}</h3>
                        <p className="text-gray-400 text-sm">{crop.variety}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <HealthIcon className={`w-4 h-4 ${getHealthColor(crop.health)}`} />
                        <span className={`text-xs capitalize ${getHealthColor(crop.health)}`}>
                          {crop.health}
                        </span>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-3">
                      <div className="flex justify-between text-xs text-gray-400 mb-1">
                        <span>{crop.stage}</span>
                        <span>{crop.daysToHarvest} days to harvest</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${crop.progress}%` }}
                          transition={{ duration: 1, delay: index * 0.2 }}
                          className="bg-gradient-to-r from-primary-400 to-green-400 h-2 rounded-full"
                        />
                      </div>
                    </div>

                    {/* Next Action */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-sm text-gray-300">
                        <Sun className="w-4 h-4 text-yellow-400" />
                        <span>{crop.nextAction}</span>
                      </div>
                      <button className="text-primary-400 hover:text-primary-300 text-xs font-medium">
                        Update
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default CropTimeline;