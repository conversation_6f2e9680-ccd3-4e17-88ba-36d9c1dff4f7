import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './context/AuthContext';
import AuthPage from './components/Auth/AuthPage';
import Sidebar from './components/Layout/Sidebar';
import Header from './components/Layout/Header';
import Dashboard from './components/Dashboard/Dashboard';
import SmartAnalysis from './components/SmartAnalysis/SmartAnalysis';
import SoilAnalysis from './components/SoilAnalysis/SoilAnalysis';
import MarketAnalyzer from './components/MarketAnalyzer/MarketAnalyzer';

const AppContent: React.FC = () => {
  const { user } = useAuth();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  if (!user) {
    return <AuthPage />;
  }

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <Dashboard />;
      case 'analysis':
        return <SmartAnalysis />;
      case 'soil':
        return <SoilAnalysis />;
      case 'market':
        return <MarketAnalyzer />;
      case 'crops':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white mb-4">Crop Intelligence</h1>
            <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-8 text-center">
              <p className="text-gray-400">Advanced crop management features coming soon...</p>
            </div>
          </div>
        );
      case 'prevention':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white mb-4">Disease Prevention</h1>
            <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-8 text-center">
              <p className="text-gray-400">Disease prevention and early warning system coming soon...</p>
            </div>
          </div>
        );
      case 'diary':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white mb-4">Farm Diary</h1>
            <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-8 text-center">
              <p className="text-gray-400">Digital farm diary with AI insights coming soon...</p>
            </div>
          </div>
        );
      case 'education':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white mb-4">Education Hub</h1>
            <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-8 text-center">
              <p className="text-gray-400">Educational resources and tutorials coming soon...</p>
            </div>
          </div>
        );
      case 'sourcing':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white mb-4">Input Sourcing</h1>
            <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-8 text-center">
              <p className="text-gray-400">Agricultural input sourcing platform coming soon...</p>
            </div>
          </div>
        );
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-navy-900 via-purple-900/50 to-primary-900/50">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{ 
            rotate: [0, 360],
            scale: [1, 1.2, 1]
          }}
          transition={{ 
            duration: 30, 
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"
        />
        <motion.div
          animate={{ 
            rotate: [360, 0],
            scale: [1.2, 1, 1.2]
          }}
          transition={{ 
            duration: 35, 
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"
        />
      </div>

      <div className="flex h-screen relative">
        {/* Sidebar */}
        <Sidebar
          activeSection={activeSection}
          setActiveSection={setActiveSection}
          isOpen={sidebarOpen}
          setIsOpen={setSidebarOpen}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col min-w-0 lg:ml-80">
          {/* Header */}
          <Header 
            toggleSidebar={() => setSidebarOpen(!sidebarOpen)} 
            sidebarOpen={sidebarOpen}
          />
          
          {/* Main Content */}
          <main className="flex-1 overflow-y-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="h-full"
              >
                {renderContent()}
              </motion.div>
            </AnimatePresence>
          </main>
        </div>
      </div>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: '#1f2937',
            color: '#fff',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          },
        }}
      />
    </AuthProvider>
  );
}

export default App;