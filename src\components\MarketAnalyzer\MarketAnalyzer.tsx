import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Search, MapPin, DollarSign, Users, Calendar, Target, AlertCircle } from 'lucide-react';
import { aiService } from '../../services/aiService';
import { MarketData } from '../../types';
import { countries } from '../../data/countries';
import toast from 'react-hot-toast';

const MarketAnalyzer: React.FC = () => {
  const [produce, setProduce] = useState('');
  const [quality, setQuality] = useState<'high' | 'medium' | 'low'>('medium');
  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [marketData, setMarketData] = useState<MarketData | null>(null);

  const handleAnalysis = async () => {
    if (!produce || !selectedCountry || !selectedProvince) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsAnalyzing(true);
    try {
      const result = await aiService.getMarketAnalysis(produce, quality, selectedCountry, selectedProvince);
      setMarketData(result);
      toast.success('Market analysis completed!');
    } catch (error) {
      toast.error('Analysis failed. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const selectedCountryData = countries.find(c => c.name === selectedCountry);

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'rising': return 'text-green-400 bg-green-500/10';
      case 'falling': return 'text-red-400 bg-red-500/10';
      case 'stable': return 'text-blue-400 bg-blue-500/10';
      default: return 'text-gray-400 bg-gray-500/10';
    }
  };

  const getDemandColor = (demand: string) => {
    switch (demand) {
      case 'high': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const commonProduce = [
    'Maize', 'Tomatoes', 'Beans', 'Wheat', 'Rice', 'Potatoes', 'Onions', 'Carrots',
    'Cabbage', 'Spinach', 'Lettuce', 'Peppers', 'Cucumbers', 'Bananas', 'Oranges',
    'Apples', 'Mangoes', 'Avocados', 'Coffee', 'Tea', 'Cotton', 'Sugarcane'
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="p-6 space-y-8"
    >
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4"
        >
          <TrendingUp className="w-8 h-8 text-white" />
        </motion.div>
        <h1 className="text-3xl font-bold text-white mb-2">Market Analyzer</h1>
        <p className="text-gray-400">Get real-time market prices and selling recommendations for your produce</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* Produce Selection */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Search className="w-5 h-5 text-yellow-400" />
              Produce Information
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Produce Name *</label>
                <input
                  type="text"
                  value={produce}
                  onChange={(e) => setProduce(e.target.value)}
                  placeholder="e.g., Maize, Tomatoes, Beans"
                  list="produce-suggestions"
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                />
                <datalist id="produce-suggestions">
                  {commonProduce.map(item => (
                    <option key={item} value={item} />
                  ))}
                </datalist>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Quality Grade</label>
                <select
                  value={quality}
                  onChange={(e) => setQuality(e.target.value as 'high' | 'medium' | 'low')}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                >
                  <option value="high" className="bg-navy-900">High Quality (Premium)</option>
                  <option value="medium" className="bg-navy-900">Medium Quality (Standard)</option>
                  <option value="low" className="bg-navy-900">Low Quality (Budget)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Location Selection */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-blue-400" />
              Location
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Country *</label>
                <select
                  value={selectedCountry}
                  onChange={(e) => {
                    setSelectedCountry(e.target.value);
                    setSelectedProvince('');
                  }}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                >
                  <option value="">Select Country</option>
                  {countries.map(country => (
                    <option key={country.code} value={country.name} className="bg-navy-900">
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Province/State *</label>
                <select
                  value={selectedProvince}
                  onChange={(e) => setSelectedProvince(e.target.value)}
                  disabled={!selectedCountry}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500 disabled:opacity-50"
                >
                  <option value="">Select Province</option>
                  {selectedCountryData?.provinces.map(province => (
                    <option key={province.code} value={province.name} className="bg-navy-900">
                      {province.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Market Tips */}
          <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-3">💡 Market Tips</h3>
            <ul className="text-sm text-gray-300 space-y-2">
              <li>• Check prices regularly for trends</li>
              <li>• Consider seasonal demand patterns</li>
              <li>• Build relationships with multiple buyers</li>
              <li>• Time your sales for maximum profit</li>
            </ul>
          </div>

          {/* Analyze Button */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleAnalysis}
            disabled={isAnalyzing || !produce || !selectedCountry || !selectedProvince}
            className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isAnalyzing ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Search className="w-5 h-5" />
                </motion.div>
                Analyzing Market...
              </>
            ) : (
              <>
                <TrendingUp className="w-5 h-5" />
                Analyze Market
              </>
            )}
          </motion.button>
        </motion.div>

        {/* Results Section */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {marketData ? (
            <>
              {/* Current Price */}
              <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                    <DollarSign className="w-5 h-5 text-green-400" />
                    Current Market Price
                  </h3>
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${getTrendColor(marketData.marketTrend)}`}>
                    {marketData.marketTrend?.toUpperCase()}
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-4xl font-bold text-green-400 mb-2">
                    ${marketData.currentPrice} <span className="text-lg text-gray-400">{marketData.currency}</span>
                  </p>
                  <p className="text-gray-400">per unit in {marketData.city}</p>
                  <div className="flex items-center justify-center gap-2 mt-2">
                    <span className={`text-sm ${marketData.priceChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {marketData.priceChange >= 0 ? '↗' : '↘'} {Math.abs(marketData.priceChange)}%
                    </span>
                    <span className="text-gray-400 text-sm">this week</span>
                  </div>
                </div>
              </div>

              {/* Market Overview */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Market Overview</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/5 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="w-4 h-4 text-blue-400" />
                      <span className="text-gray-400 text-sm">Demand Level</span>
                    </div>
                    <p className={`font-semibold ${getDemandColor(marketData.demand)}`}>
                      {marketData.demand?.toUpperCase()}
                    </p>
                  </div>
                  <div className="bg-white/5 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="w-4 h-4 text-purple-400" />
                      <span className="text-gray-400 text-sm">Season</span>
                    </div>
                    <p className="text-white font-semibold">{marketData.season}</p>
                  </div>
                </div>
              </div>

              {/* Best Buyers */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <Target className="w-5 h-5 text-green-400" />
                  Best Buyers
                </h3>
                <div className="space-y-3">
                  {marketData.bestBuyers.map((buyer, index) => (
                    <div key={index} className="bg-white/5 rounded-xl p-4 flex items-center justify-between">
                      <span className="text-white">{buyer}</span>
                      <span className="text-green-400 text-sm font-medium">Contact Available</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Recommendations */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <AlertCircle className="w-5 h-5 text-yellow-400" />
                  Selling Recommendations
                </h3>
                <ul className="space-y-3">
                  {marketData.recommendations.map((recommendation, index) => (
                    <li key={index} className="text-gray-300 flex items-start gap-3">
                      <span className="text-yellow-400 font-bold bg-yellow-500/20 rounded-full w-6 h-6 flex items-center justify-center text-sm flex-shrink-0 mt-0.5">
                        {index + 1}
                      </span>
                      <span>{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Price History Chart Placeholder */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Price Trend (Last 30 Days)</h3>
                <div className="h-32 bg-white/5 rounded-xl flex items-center justify-center">
                  <p className="text-gray-400">Price chart visualization coming soon</p>
                </div>
              </div>
            </>
          ) : (
            <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-12 text-center">
              <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Ready for Market Analysis</h3>
              <p className="text-gray-400">
                Enter your produce details and location to get current market prices, buyer information, and selling recommendations
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default MarketAnalyzer;