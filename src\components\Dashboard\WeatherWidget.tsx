import React from 'react';
import { motion } from 'framer-motion';
import { Cloud, Sun, CloudRain, Wind, Droplets, Thermometer } from 'lucide-react';

const WeatherWidget: React.FC = () => {
  const currentWeather = {
    temperature: 24,
    condition: 'Partly Cloudy',
    humidity: 68,
    windSpeed: 12,
    rainfall: 0,
    uvIndex: 6
  };

  const forecast = [
    { day: 'Today', icon: Sun, temp: 24, condition: 'Sunny' },
    { day: 'Tomorrow', icon: Cloud, temp: 22, condition: 'Cloudy' },
    { day: 'Thu', icon: CloudRain, temp: 19, condition: 'Rain' },
    { day: 'Fri', icon: Sun, temp: 26, condition: 'Sunny' },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
    >
      <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
        <Cloud className="w-5 h-5 text-blue-400" />
        Weather
      </h3>

      {/* Current Weather */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-3">
          <Sun className="w-8 h-8 text-white" />
        </div>
        <h4 className="text-3xl font-bold text-white mb-1">{currentWeather.temperature}°C</h4>
        <p className="text-gray-300 text-sm">{currentWeather.condition}</p>
      </div>

      {/* Weather Details */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-white/5 rounded-xl p-3">
          <div className="flex items-center gap-2 mb-1">
            <Droplets className="w-4 h-4 text-blue-400" />
            <span className="text-xs text-gray-400">Humidity</span>
          </div>
          <p className="text-white font-semibold">{currentWeather.humidity}%</p>
        </div>
        <div className="bg-white/5 rounded-xl p-3">
          <div className="flex items-center gap-2 mb-1">
            <Wind className="w-4 h-4 text-gray-400" />
            <span className="text-xs text-gray-400">Wind</span>
          </div>
          <p className="text-white font-semibold">{currentWeather.windSpeed} km/h</p>
        </div>
      </div>

      {/* Forecast */}
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-gray-300">4-Day Forecast</h4>
        {forecast.map((day, index) => (
          <motion.div
            key={day.day}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center justify-between py-2"
          >
            <div className="flex items-center gap-3">
              <day.icon className="w-4 h-4 text-blue-400" />
              <span className="text-sm text-white">{day.day}</span>
            </div>
            <div className="text-right">
              <p className="text-sm text-white font-semibold">{day.temp}°C</p>
              <p className="text-xs text-gray-400">{day.condition}</p>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default WeatherWidget;