#!/usr/bin/env npx tsx

/**
 * Test script to verify Brazil-specific market analysis
 * This tests the location-specific market data for Brazil/Ceará
 */

import { WebCrawlingService } from '../services/webCrawlingService';

async function testBrazilMarketAnalysis() {
  console.log('🇧🇷 Testing Brazil-specific market analysis...\n');
  
  const webCrawlingService = new WebCrawlingService();
  
  try {
    // Test with Brazil/Ceará location
    console.log('📍 Testing with location: Brazil, Ceará');
    const brazilResult = await (webCrawlingService as any).getCropMarketAnalysis('tomato', 'Brazil, Ceará');
    
    console.log('\n🏪 BRAZIL MARKET ANALYSIS RESULT:');
    console.log('=====================================');
    
    console.log('\n💰 Market Prices:');
    brazilResult.marketPrices.forEach((price: any, index: number) => {
      console.log(`${index + 1}. ${price.location}`);
      console.log(`   Price: ${price.pricePerKg}`);
      console.log(`   Platform: ${price.platform}`);
      console.log(`   Website: ${price.website}`);
      console.log(`   Trend: ${price.trend}\n`);
    });
    
    console.log('🎯 Best Recommendation:');
    console.log(`Market: ${brazilResult.bestRecommendation.market}`);
    console.log(`Reason: ${brazilResult.bestRecommendation.reason}`);
    console.log(`Expected Price: ${brazilResult.bestRecommendation.expectedPrice}`);
    console.log(`Contact: ${brazilResult.bestRecommendation.contactInfo}`);
    console.log(`Website: ${brazilResult.bestRecommendation.website}`);
    
    console.log('\n💡 Market Insights:');
    brazilResult.marketInsights.forEach((insight: string, index: number) => {
      console.log(`${index + 1}. ${insight}`);
    });
    
    // Verify Brazil-specific data
    const hasBrazilData = brazilResult.marketPrices.some((price: any) => 
      price.location.includes('CEASA') || 
      price.location.includes('Fortaleza') || 
      price.location.includes('São Paulo') ||
      price.pricePerKg.includes('R$')
    );
    
    if (hasBrazilData) {
      console.log('\n✅ SUCCESS: Brazil-specific market data detected!');
      console.log('✅ Location-specific pricing in Brazilian Reais (R$) found');
      console.log('✅ CEASA and Brazilian market platforms included');
    } else {
      console.log('\n❌ FAILURE: Generic data returned instead of Brazil-specific data');
    }
    
    // Test with Zimbabwe for comparison
    console.log('\n\n📍 Testing with location: Zimbabwe, Harare (for comparison)');
    const zimbabweResult = await (webCrawlingService as any).getCropMarketAnalysis('tomato', 'Zimbabwe, Harare');
    
    const hasZimbabweData = zimbabweResult.marketPrices.some((price: any) => 
      price.location.includes('Harare') || 
      price.location.includes('Bulawayo') ||
      price.platform.includes('ZimPriceCheck')
    );
    
    if (hasZimbabweData) {
      console.log('✅ Zimbabwe-specific data working correctly');
    } else {
      console.log('❌ Zimbabwe-specific data not working');
    }
    
    console.log('\n🎉 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testBrazilMarketAnalysis();
