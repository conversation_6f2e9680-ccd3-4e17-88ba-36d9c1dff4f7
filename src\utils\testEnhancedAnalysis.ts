// Test Enhanced Analysis System - Verify Extremely Specific Treatment and Market Data

import { aiService } from '../services/aiService';

export async function testEnhancedAnalysisSystem() {
  console.log('🧪 TESTING ENHANCED ANALYSIS SYSTEM...');
  console.log('🎯 Testing extremely specific treatment recommendations and market analysis...');

  try {
    // Test case: Sick cattle with specific symptoms
    const testInputs = {
      analysisType: 'livestock',
      cropOrAnimalType: 'cattle',
      symptoms: 'sleeping continuously, not aware, unable to stand, down syndrome',
      location: 'Midlands, Zimbabwe'
    };

    console.log('📋 Test Inputs:', testInputs);

    // Create a mock image file for testing
    const mockImageFile = new File(['mock image data'], 'test-cattle.jpg', { type: 'image/jpeg' });

    // Perform enhanced analysis
    const result = await aiService.performExtremeIntelligentAnalysis(
      'smart_analysis',
      testInputs.cropOrAnimalType,
      testInputs.symptoms,
      testInputs.location,
      mockImageFile
    );

    console.log('✅ ENHANCED ANALYSIS COMPLETED');
    console.log('🔍 TESTING TREATMENT SPECIFICITY...');

    // Test treatment specificity
    if (result.treatment && result.treatment.length > 0) {
      console.log('✅ Treatment steps found:', result.treatment.length);
      
      result.treatment.forEach((step, index) => {
        console.log(`\n📋 TREATMENT STEP ${index + 1}:`);
        console.log(`   Content: ${step}`);
        
        // Check for specific medication details
        const hasMedication = step.includes('MEDICATION:');
        const hasQuantity = step.includes('QUANTITY:');
        const hasApplication = step.includes('APPLICATION:');
        const hasWhereToBuy = step.includes('WHERE TO BUY:');
        const hasEquipment = step.includes('EQUIPMENT:');
        
        console.log(`   ✅ Has Medication: ${hasMedication}`);
        console.log(`   ✅ Has Quantity: ${hasQuantity}`);
        console.log(`   ✅ Has Application: ${hasApplication}`);
        console.log(`   ✅ Has Where to Buy: ${hasWhereToBuy}`);
        console.log(`   ✅ Has Equipment: ${hasEquipment}`);
        
        if (hasMedication && hasQuantity && hasApplication && hasWhereToBuy && hasEquipment) {
          console.log(`   🎯 STEP ${index + 1}: EXTREMELY SPECIFIC ✅`);
        } else {
          console.log(`   ⚠️ STEP ${index + 1}: NEEDS MORE SPECIFICITY`);
        }
      });
    } else {
      console.log('❌ No treatment steps found');
    }

    console.log('\n🔍 TESTING MARKET DATA SPECIFICITY...');

    // Test market data specificity
    if (result.marketData && result.marketData.currentPrices) {
      console.log('✅ Market data found');
      
      result.marketData.currentPrices.forEach((market: any, index: number) => {
        console.log(`\n💰 MARKET ${index + 1}:`);
        console.log(`   Location: ${market.location}`);
        console.log(`   Price: ${market.pricePerKg}`);
        console.log(`   Platform: ${market.platform}`);
        console.log(`   Trend: ${market.trend}`);
        console.log(`   Contact: ${market.contact || 'N/A'}`);
        console.log(`   Specific Notes: ${market.specificNotes || 'N/A'}`);
        
        // Check for condition-specific pricing
        const hasConditionNote = market.pricePerKg && (
          market.pricePerKg.includes('health') || 
          market.pricePerKg.includes('condition') ||
          market.pricePerKg.includes('Reduced') ||
          market.pricePerKg.includes('Premium')
        );
        
        console.log(`   🎯 Has Condition-Specific Pricing: ${hasConditionNote ? '✅' : '⚠️'}`);
      });

      // Test best recommendation specificity
      if (result.marketData.bestRecommendation) {
        console.log('\n🏆 BEST RECOMMENDATION:');
        console.log(`   Recommendation: ${result.marketData.bestRecommendation.recommendation}`);
        console.log(`   Expected Price: ${result.marketData.bestRecommendation.expectedPrice}`);
        console.log(`   Contact: ${result.marketData.bestRecommendation.contactInfo}`);
        console.log(`   Additional Advice: ${result.marketData.bestRecommendation.additionalAdvice || 'N/A'}`);
        
        const hasHealthAdvice = result.marketData.bestRecommendation.recommendation && (
          result.marketData.bestRecommendation.recommendation.includes('health') ||
          result.marketData.bestRecommendation.recommendation.includes('treatment') ||
          result.marketData.bestRecommendation.recommendation.includes('condition')
        );
        
        console.log(`   🎯 Has Health-Specific Advice: ${hasHealthAdvice ? '✅' : '⚠️'}`);
      }
    } else {
      console.log('❌ No market data found');
    }

    console.log('\n🔍 TESTING VALUE ADDITION SPECIFICITY...');

    // Test value addition specificity
    if (result.valueAddition && result.valueAddition.preservationPackaging) {
      console.log('✅ Value addition data found');
      
      result.valueAddition.preservationPackaging.forEach((strategy: any, index: number) => {
        console.log(`\n📦 VALUE STRATEGY ${index + 1}:`);
        console.log(`   Strategy: ${strategy.strategy}`);
        console.log(`   ROI: ${strategy.roi}`);
        console.log(`   Equipment Cost: ${strategy.equipmentCost}`);
        console.log(`   Source: ${strategy.source}`);
      });
    } else {
      console.log('❌ No value addition data found');
    }

    console.log('\n🔍 TESTING YOUTUBE TUTORIALS...');

    // Test YouTube tutorials
    if (result.videoTutorials && result.videoTutorials.length > 0) {
      console.log('✅ YouTube tutorials found:', result.videoTutorials.length);
      
      result.videoTutorials.forEach((video: any, index: number) => {
        console.log(`\n📹 VIDEO ${index + 1}:`);
        console.log(`   Title: ${video.title}`);
        console.log(`   URL: ${video.url}`);
        console.log(`   Duration: ${video.duration}`);
        console.log(`   Relevance: ${video.relevanceScore}%`);
        
        const hasWorkingURL = video.url && (
          video.url.includes('youtube.com') || 
          video.url.includes('accessagriculture.org') ||
          video.url.includes('agricdemy.com')
        );
        
        console.log(`   🎯 Has Working URL: ${hasWorkingURL ? '✅' : '⚠️'}`);
      });
    } else {
      console.log('❌ No YouTube tutorials found');
    }

    console.log('\n🎯 ENHANCED ANALYSIS TEST COMPLETED!');
    console.log('📊 SUMMARY:');
    console.log(`   ✅ Treatment Steps: ${result.treatment?.length || 0}`);
    console.log(`   ✅ Market Prices: ${result.marketData?.currentPrices?.length || 0}`);
    console.log(`   ✅ Value Strategies: ${result.valueAddition?.preservationPackaging?.length || 0}`);
    console.log(`   ✅ YouTube Videos: ${result.videoTutorials?.length || 0}`);
    
    return result;

  } catch (error) {
    console.error('❌ ENHANCED ANALYSIS TEST FAILED:', error);
    throw error;
  }
}

// Run test if this file is executed directly
if (typeof window !== 'undefined') {
  (window as any).testEnhancedAnalysis = testEnhancedAnalysisSystem;
}
