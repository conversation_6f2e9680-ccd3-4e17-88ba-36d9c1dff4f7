// Test livestock analysis with specific symptoms
import { aiService } from '../services/aiService';

export async function testLivestockAnalysis() {
  console.log('🐄 Testing Livestock Analysis System...');
  
  const testInputs = {
    analysisType: 'livestock',
    cropOrAnimalType: 'cattle',
    symptoms: 'sleeping continuously, not aware, not active, unable to stand',
    location: 'Midlands, Zimbabwe',
    imageFile: null // Simulating no image for now
  };

  try {
    console.log('📊 Running analysis with inputs:', testInputs);
    
    const result = await aiService.analyzeWithAI(
      testInputs.analysisType,
      testInputs.cropOrAnimalType,
      testInputs.symptoms,
      testInputs.location,
      testInputs.imageFile
    );

    console.log('✅ Analysis completed successfully!');
    console.log('📋 Results Summary:');
    console.log('- Diagnosis:', result.diagnosis);
    console.log('- Confidence:', result.confidence);
    console.log('- Severity:', result.severity);
    console.log('- Treatment Steps:', result.treatment?.length || 0, 'steps');
    console.log('- Follow-up Inspections:', result.followUpInspections?.length || 0, 'protocols');
    console.log('- Cost Estimate:', result.costEstimate);
    
    // Check if specific experts are included
    const treatmentText = JSON.stringify(result.treatment);
    const hasSpecificExperts = treatmentText.includes('Dr. Simbarashe Madzimure') || 
                              treatmentText.includes('Department of Veterinary Services');
    
    console.log('🏥 Specific Experts Included:', hasSpecificExperts ? '✅ YES' : '❌ NO');
    
    // Check if emergency protocols are triggered
    const isEmergency = result.severity === 'critical' || 
                       treatmentText.includes('IMMEDIATE') || 
                       treatmentText.includes('EMERGENCY');
    
    console.log('🚨 Emergency Protocols Triggered:', isEmergency ? '✅ YES' : '❌ NO');
    
    return result;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testLivestockAnalysis()
    .then(() => console.log('🎉 Test completed successfully!'))
    .catch(error => console.error('💥 Test failed:', error));
}
