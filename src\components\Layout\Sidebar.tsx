import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Home, 
  Sprout, 
  Camera, 
  TrendingUp, 
  Shield, 
  TestTube, 
  BookOpen, 
  ShoppingCart,
  PenTool,
  User,
  Settings,
  LogOut,
  X
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';

interface SidebarProps {
  activeSection: string;
  setActiveSection: (section: string) => void;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeSection, setActiveSection, isOpen, setIsOpen }) => {
  const { user, logout } = useAuth();

  const menuItems = [
    { id: 'dashboard', icon: Home, label: 'Dashboard', color: 'text-primary-400' },
    { id: 'crops', icon: Sprout, label: 'Crop Intelligence', color: 'text-green-400' },
    { id: 'analysis', icon: Camera, label: 'Smart Analysis', color: 'text-blue-400' },
    { id: 'market', icon: TrendingUp, label: 'Market Analyzer', color: 'text-yellow-400' },
    { id: 'prevention', icon: Shield, label: 'Disease Prevention', color: 'text-red-400' },
    { id: 'soil', icon: TestTube, label: 'Soil Health', color: 'text-purple-400' },
    { id: 'diary', icon: PenTool, label: 'Farm Diary', color: 'text-indigo-400' },
    { id: 'education', icon: BookOpen, label: 'Education Hub', color: 'text-orange-400' },
    { id: 'sourcing', icon: ShoppingCart, label: 'Input Sourcing', color: 'text-teal-400' },
  ];

  const handleMenuClick = (itemId: string) => {
    setActiveSection(itemId);
    // Close sidebar on mobile/tablet after selection
    if (window.innerWidth < 1024) {
      setIsOpen(false);
    }
  };

  return (
    <>
      {/* Mobile/Tablet Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <div className={`
        fixed left-0 top-0 h-full w-80 bg-navy-900/95 backdrop-blur-xl z-50 overflow-y-auto
        transform transition-transform duration-300 ease-in-out
        lg:translate-x-0 lg:static lg:z-auto
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-6 border-b border-primary-500/20 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <motion.div 
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  className="w-10 h-10 bg-gradient-to-br from-primary-400 to-purple-500 rounded-xl flex items-center justify-center"
                >
                  <Sprout className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                  <h1 className="text-xl font-bold text-white">ZeusAgriApp</h1>
                  <p className="text-xs text-gray-400">AI-Powered Farming</p>
                </div>
              </div>
              
              {/* Close button for mobile/tablet */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setIsOpen(false)}
                className="lg:hidden p-2 rounded-lg text-gray-400 hover:text-white hover:bg-white/10 transition-colors"
              >
                <X className="w-5 h-5" />
              </motion.button>
            </div>
          </div>

          {/* User Info */}
          <div className="p-6 border-b border-primary-500/20 flex-shrink-0">
            <div className="flex items-center gap-3">
              <motion.div 
                whileHover={{ scale: 1.05 }}
                className="w-12 h-12 bg-gradient-to-br from-primary-400 to-blue-500 rounded-full flex items-center justify-center"
              >
                <User className="w-6 h-6 text-white" />
              </motion.div>
              <div className="flex-1 min-w-0">
                <h3 className="text-white font-semibold truncate">{user?.name}</h3>
                <p className="text-xs text-gray-400 capitalize">{user?.role}</p>
                {user?.farmerId && (
                  <p className="text-xs text-primary-400">ID: {user.farmerId}</p>
                )}
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {menuItems.map((item, index) => {
              const isActive = activeSection === item.id;
              return (
                <motion.button
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ scale: 1.02, x: 4 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleMenuClick(item.id)}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                    isActive
                      ? 'bg-primary-500/20 text-primary-400 shadow-lg shadow-primary-500/20 border border-primary-500/30'
                      : 'text-gray-400 hover:text-white hover:bg-white/5 border border-transparent'
                  }`}
                >
                  <item.icon className={`w-5 h-5 flex-shrink-0 ${isActive ? item.color : ''}`} />
                  <span className="font-medium truncate">{item.label}</span>
                  {isActive && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="ml-auto w-2 h-2 bg-primary-400 rounded-full flex-shrink-0"
                    />
                  )}
                </motion.button>
              );
            })}
          </nav>

          {/* Bottom Actions */}
          <div className="p-4 border-t border-primary-500/20 space-y-2 flex-shrink-0">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full flex items-center gap-3 px-4 py-3 rounded-xl text-gray-400 hover:text-white hover:bg-white/5 transition-all duration-200 border border-transparent hover:border-white/10"
            >
              <Settings className="w-5 h-5" />
              <span className="font-medium">Settings</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={logout}
              className="w-full flex items-center gap-3 px-4 py-3 rounded-xl text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200 border border-transparent hover:border-red-500/20"
            >
              <LogOut className="w-5 h-5" />
              <span className="font-medium">Logout</span>
            </motion.button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;