import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { TestTube, Upload, MapPin, TrendingUp, DollarSign, Clock, AlertTriangle, Video, Sprout } from 'lucide-react';
import { aiService } from '../../services/aiService';
import { SoilAnalysisResult } from '../../types';
import { countries } from '../../data/countries';
import toast from 'react-hot-toast';

const SoilAnalysis: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<SoilAnalysisResult | null>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Image size should be less than 10MB');
        return;
      }
      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAnalysis = async () => {
    if (!selectedImage || !selectedCountry || !selectedProvince) {
      toast.error('Please upload soil image and select location');
      return;
    }

    setIsAnalyzing(true);
    try {
      const result = await aiService.analyzeSoilHealth(
        selectedImage.split(',')[1],
        selectedCountry,
        selectedProvince
      );
      setAnalysisResult(result);
      toast.success('Soil analysis completed successfully!');
    } catch (error) {
      toast.error('Analysis failed. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const selectedCountryData = countries.find(c => c.name === selectedCountry);

  const getProfitabilityColor = (profitability: string) => {
    switch (profitability) {
      case 'high': return 'text-green-400 bg-green-500/10';
      case 'medium': return 'text-yellow-400 bg-yellow-500/10';
      case 'low': return 'text-red-400 bg-red-500/10';
      default: return 'text-gray-400 bg-gray-500/10';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="p-6 space-y-8"
    >
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4"
        >
          <TestTube className="w-8 h-8 text-white" />
        </motion.div>
        <h1 className="text-3xl font-bold text-white mb-2">Soil Health Analysis</h1>
        <p className="text-gray-400">AI-powered soil analysis with crop recommendations and profitability insights</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* Location Selection */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-blue-400" />
              Location
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Country *</label>
                <select
                  value={selectedCountry}
                  onChange={(e) => {
                    setSelectedCountry(e.target.value);
                    setSelectedProvince('');
                  }}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                >
                  <option value="">Select Country</option>
                  {countries.map(country => (
                    <option key={country.code} value={country.name} className="bg-navy-900">
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Province/State *</label>
                <select
                  value={selectedProvince}
                  onChange={(e) => setSelectedProvince(e.target.value)}
                  disabled={!selectedCountry}
                  className="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500 disabled:opacity-50"
                >
                  <option value="">Select Province</option>
                  {selectedCountryData?.provinces.map(province => (
                    <option key={province.code} value={province.name} className="bg-navy-900">
                      {province.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Soil Image Upload */}
          <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <TestTube className="w-5 h-5 text-purple-400" />
              Upload Soil Sample Image
            </h3>
            
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <div className="border-2 border-dashed border-gray-600 rounded-xl p-8 text-center hover:border-primary-500 transition-colors">
                {selectedImage ? (
                  <div className="space-y-4">
                    <img
                      src={selectedImage}
                      alt="Soil sample"
                      className="max-h-48 mx-auto rounded-lg"
                    />
                    <p className="text-green-400 text-sm">Soil image uploaded successfully</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-white font-medium">Upload soil sample image</p>
                      <p className="text-gray-400 text-sm">Clear photo of soil sample - JPG, PNG up to 10MB</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Analysis Tips */}
          <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-3">📸 Photo Tips</h3>
            <ul className="text-sm text-gray-300 space-y-2">
              <li>• Take photo in natural daylight</li>
              <li>• Show soil texture and color clearly</li>
              <li>• Include a reference object for scale</li>
              <li>• Avoid shadows and reflections</li>
            </ul>
          </div>

          {/* Analyze Button */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleAnalysis}
            disabled={isAnalyzing || !selectedImage || !selectedCountry || !selectedProvince}
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isAnalyzing ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <TestTube className="w-5 h-5" />
                </motion.div>
                Analyzing Soil...
              </>
            ) : (
              <>
                <TestTube className="w-5 h-5" />
                Analyze Soil
              </>
            )}
          </motion.button>
        </motion.div>

        {/* Results Section */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {analysisResult ? (
            <>
              {/* Soil Properties */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <TestTube className="w-5 h-5 text-purple-400" />
                  Soil Properties
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/5 rounded-xl p-4">
                    <p className="text-gray-400 text-sm">Soil Type</p>
                    <p className="text-white font-semibold">{analysisResult.soilType}</p>
                  </div>
                  <div className="bg-white/5 rounded-xl p-4">
                    <p className="text-gray-400 text-sm">pH Level</p>
                    <p className="text-white font-semibold">{analysisResult.phLevel}</p>
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-gray-400 text-sm mb-2">Nutrient Levels</p>
                  <div className="grid grid-cols-3 gap-2">
                    <div className="bg-white/5 rounded-lg p-3 text-center">
                      <p className="text-xs text-gray-400">Nitrogen</p>
                      <p className="text-white font-medium">{analysisResult.nutrients.nitrogen}</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3 text-center">
                      <p className="text-xs text-gray-400">Phosphorus</p>
                      <p className="text-white font-medium">{analysisResult.nutrients.phosphorus}</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3 text-center">
                      <p className="text-xs text-gray-400">Potassium</p>
                      <p className="text-white font-medium">{analysisResult.nutrients.potassium}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recommended Crops */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <Sprout className="w-5 h-5 text-green-400" />
                  Recommended Crops
                </h3>
                <div className="space-y-4">
                  {analysisResult.recommendedCrops.map((crop, index) => (
                    <div key={index} className="bg-white/5 rounded-xl p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="text-white font-semibold">{crop.name}</h4>
                          <p className="text-gray-400 text-sm">{crop.variety}</p>
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${getProfitabilityColor(crop.profitability)}`}>
                          {crop.profitability?.toUpperCase()} PROFIT
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-400">Planting Time</p>
                          <p className="text-white">{crop.plantingTime}</p>
                        </div>
                        <div>
                          <p className="text-gray-400">Harvest Time</p>
                          <p className="text-white">{crop.harvestTime}</p>
                        </div>
                        <div>
                          <p className="text-gray-400">Expected Yield</p>
                          <p className="text-white">{crop.expectedYield}</p>
                        </div>
                        <div>
                          <p className="text-gray-400">Market Value</p>
                          <p className="text-green-400 font-semibold">${crop.marketValue}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Cost-Benefit Analysis */}
              <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <DollarSign className="w-5 h-5 text-green-400" />
                  Cost-Benefit Analysis
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/10 rounded-xl p-4">
                    <p className="text-gray-400 text-sm">Initial Investment</p>
                    <p className="text-white font-bold text-xl">${analysisResult.costBenefitAnalysis.initialInvestment}</p>
                  </div>
                  <div className="bg-white/10 rounded-xl p-4">
                    <p className="text-gray-400 text-sm">Expected Revenue</p>
                    <p className="text-green-400 font-bold text-xl">${analysisResult.costBenefitAnalysis.expectedRevenue}</p>
                  </div>
                  <div className="bg-white/10 rounded-xl p-4">
                    <p className="text-gray-400 text-sm">Profit Margin</p>
                    <p className="text-green-400 font-bold text-xl">{analysisResult.costBenefitAnalysis.profitMargin}%</p>
                  </div>
                  <div className="bg-white/10 rounded-xl p-4">
                    <p className="text-gray-400 text-sm">Break-even Time</p>
                    <p className="text-white font-bold">{analysisResult.costBenefitAnalysis.breakEvenTime}</p>
                  </div>
                </div>
                <div className="mt-4 flex items-center gap-2">
                  <AlertTriangle className={`w-4 h-4 ${getRiskColor(analysisResult.costBenefitAnalysis.riskLevel)}`} />
                  <span className="text-gray-400 text-sm">Risk Level:</span>
                  <span className={`font-semibold ${getRiskColor(analysisResult.costBenefitAnalysis.riskLevel)}`}>
                    {analysisResult.costBenefitAnalysis.riskLevel?.toUpperCase()}
                  </span>
                </div>
              </div>

              {/* Video Tutorials */}
              {analysisResult.videoTutorials && analysisResult.videoTutorials.length > 0 && (
                <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                    <Video className="w-5 h-5 text-red-400" />
                    Soil Improvement Tutorials
                  </h3>
                  <div className="space-y-3">
                    {analysisResult.videoTutorials.map((video, index) => (
                      <div key={index} className="bg-white/5 rounded-xl p-4 hover:bg-white/10 transition-colors">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="text-white font-medium flex-1">{video.title}</h4>
                          <div className="flex items-center gap-2 text-xs text-gray-400">
                            <Clock className="w-3 h-3" />
                            {video.duration}
                          </div>
                        </div>
                        <p className="text-gray-400 text-sm mb-3">{video.description}</p>
                        <button className="text-red-400 hover:text-red-300 text-sm font-medium">
                          Watch Tutorial
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Best Practices */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Best Practices</h3>
                <ul className="space-y-2">
                  {analysisResult.bestPractices.map((practice, index) => (
                    <li key={index} className="text-gray-300 flex items-start gap-2">
                      <span className="text-green-400 mt-1">✓</span>
                      <span>{practice}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Expected Diseases */}
              <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-400" />
                  Potential Diseases to Watch
                </h3>
                <ul className="space-y-2">
                  {analysisResult.expectedDiseases.map((disease, index) => (
                    <li key={index} className="text-gray-300 flex items-start gap-2">
                      <span className="text-yellow-400 mt-1">⚠</span>
                      <span>{disease}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </>
          ) : (
            <div className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-12 text-center">
              <TestTube className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Ready for Soil Analysis</h3>
              <p className="text-gray-400">
                Upload a soil sample image and select your location to get comprehensive soil analysis with crop recommendations and profitability insights
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default SoilAnalysis;