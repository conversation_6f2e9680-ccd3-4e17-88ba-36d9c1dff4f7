import React from 'react';
import { motion } from 'framer-motion';
import DashboardStats from './DashboardStats';
import CropTimeline from './CropTimeline';
import WeatherWidget from './WeatherWidget';
import RecentActivity from './RecentActivity';
import { useAuth } from '../../context/AuthContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-8"
    >
      {/* Welcome Section */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-blue-500/20 backdrop-blur-xl border border-white/10 p-8">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-transparent" />
        <div className="relative z-10">
          <motion.h1
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-3xl font-bold text-white mb-2"
          >
            Welcome back, {user?.name}! 🌱
          </motion.h1>
          <motion.p
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-gray-300"
          >
            {user?.role === 'farmer' ? 
              `Your farm ID: ${user.farmerId} | Let's check on your crops today` :
              `Role: ${user.role} | Monitor and assist farming operations`
            }
          </motion.p>
        </div>
      </div>

      {/* Stats Grid */}
      <DashboardStats />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Crop Timeline - Takes 2 columns */}
        <div className="lg:col-span-2">
          <CropTimeline />
        </div>

        {/* Right Sidebar */}
        <div className="space-y-6">
          <WeatherWidget />
          <RecentActivity />
        </div>
      </div>
    </motion.div>
  );
};

export default Dashboard;