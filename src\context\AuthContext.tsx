import React, { createContext, useContext, useState, useEffect } from 'react';
import { User } from '../types';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string, role: string) => Promise<boolean>;
  register: (userData: Partial<User>, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for saved user in localStorage
    const savedUser = localStorage.getItem('zeusagri_user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setIsLoading(false);
  }, []);

  const generateFarmerId = () => {
    const existingIds = JSON.parse(localStorage.getItem('zeusagri_farmer_ids') || '[]');
    const newId = `farm${String(existingIds.length + 1).padStart(3, '0')}`;
    existingIds.push(newId);
    localStorage.setItem('zeusagri_farmer_ids', JSON.stringify(existingIds));
    return newId;
  };

  const login = async (email: string, password: string, role: string): Promise<boolean> => {
    setIsLoading(true);
    
    // Admin passphrase check for government/advisor roles
    if ((role === 'government' || role === 'advisor') && password !== 'zeus-farm24') {
      setIsLoading(false);
      return false;
    }

    // Simulate login process
    await new Promise(resolve => setTimeout(resolve, 1000));

    const mockUser: User = {
      id: `user_${Date.now()}`,
      name: email.split('@')[0],
      email,
      role: role as User['role'],
      farmerId: role === 'farmer' ? generateFarmerId() : undefined,
      verified: true,
      createdAt: new Date(),
    };

    setUser(mockUser);
    localStorage.setItem('zeusagri_user', JSON.stringify(mockUser));
    setIsLoading(false);
    return true;
  };

  const register = async (userData: Partial<User>, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    // Admin passphrase check for government/advisor roles
    if ((userData.role === 'government' || userData.role === 'advisor') && password !== 'zeus-farm24') {
      setIsLoading(false);
      return false;
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    const newUser: User = {
      id: `user_${Date.now()}`,
      name: userData.name || '',
      email: userData.email || '',
      role: userData.role || 'farmer',
      farmerId: userData.role === 'farmer' ? generateFarmerId() : undefined,
      location: userData.location,
      cropTypes: userData.cropTypes,
      livestockTypes: userData.livestockTypes,
      verified: true,
      createdAt: new Date(),
    };

    setUser(newUser);
    localStorage.setItem('zeusagri_user', JSON.stringify(newUser));
    setIsLoading(false);
    return true;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('zeusagri_user');
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};