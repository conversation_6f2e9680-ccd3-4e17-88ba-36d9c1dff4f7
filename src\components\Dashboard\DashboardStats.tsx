import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, AlertTriangle, CheckCircle } from 'lucide-react';

const DashboardStats: React.FC = () => {
  const stats = [
    {
      title: 'Active Crops',
      value: '12',
      change: '+2 this month',
      trend: 'up',
      icon: CheckCircle,
      color: 'from-green-400 to-emerald-500',
      bgColor: 'bg-green-500/10',
    },
    {
      title: 'Health Score',
      value: '87%',
      change: '+5% this week',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-blue-400 to-cyan-500',
      bgColor: 'bg-blue-500/10',
    },
    {
      title: 'Market Value',
      value: '$2,840',
      change: '-3% this week',
      trend: 'down',
      icon: TrendingDown,
      color: 'from-yellow-400 to-orange-500',
      bgColor: 'bg-yellow-500/10',
    },
    {
      title: 'Alerts',
      value: '3',
      change: '2 new today',
      trend: 'warning',
      icon: AlertTriangle,
      color: 'from-red-400 to-pink-500',
      bgColor: 'bg-red-500/10',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          whileHover={{ scale: 1.02, y: -2 }}
          className="relative group"
        >
          <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl"
               style={{ background: `linear-gradient(135deg, ${stat.color.split(' ')[1]}, ${stat.color.split(' ')[3]})` }} />
          
          <div className={`relative ${stat.bgColor} backdrop-blur-xl border border-white/10 rounded-2xl p-6 transition-all duration-300`}>
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gradient-to-r ${stat.color}`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <div className={`text-xs px-2 py-1 rounded-full ${
                stat.trend === 'up' ? 'bg-green-500/20 text-green-400' :
                stat.trend === 'down' ? 'bg-red-500/20 text-red-400' :
                'bg-yellow-500/20 text-yellow-400'
              }`}>
                {stat.change}
              </div>
            </div>
            
            <div>
              <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
              <p className="text-gray-400 text-sm">{stat.title}</p>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default DashboardStats;