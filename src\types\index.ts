export interface User {
  id: string;
  name: string;
  email: string;
  role: 'farmer' | 'advisor' | 'government' | 'ngo';
  farmerId?: string;
  location?: string;
  country?: string;
  province?: string;
  cropTypes?: string[];
  livestockTypes?: string[];
  profileImage?: string;
  verified: boolean;
  createdAt: Date;
}

export interface CropData {
  id: string;
  name: string;
  variety: string;
  plantingDate: Date;
  expectedHarvest: Date;
  currentStage: string;
  healthStatus: 'healthy' | 'warning' | 'critical';
  location: string;
  area: number;
  notes: string[];
  images?: string[];
}

export interface LivestockData {
  id: string;
  type: string;
  breed: string;
  age: number;
  healthStatus: 'healthy' | 'warning' | 'critical';
  lastCheckup: Date;
  vaccinations: string[];
  notes: string[];
  images?: string[];
}

export interface DiaryEntry {
  id: string;
  date: Date;
  title: string;
  description: string;
  images?: string[];
  tags: string[];
  aiInsights?: string[];
  weather?: {
    temperature: number;
    humidity: number;
    rainfall: number;
  };
  cropId?: string;
  livestockId?: string;
}

export interface MarketData {
  id: string;
  produce: string;
  country: string;
  city: string;
  quality: 'high' | 'medium' | 'low';
  currentPrice: number;
  currency: string;
  priceChange: number;
  bestBuyers: string[];
  demand: 'high' | 'medium' | 'low';
  season: string;
  marketTrend: 'rising' | 'falling' | 'stable';
  recommendations: string[];
}

export interface AIAnalysisResult {
  diagnosis: string;
  confidence: number;
  treatment: string[];
  prevention: string[];
  estimatedRecovery: string;
  videoTutorials: VideoTutorial[];
  additionalResources: string[];
  costEstimate?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface SoilAnalysisResult {
  soilType: string;
  phLevel: number;
  nutrients: {
    nitrogen: string;
    phosphorus: string;
    potassium: string;
  };
  recommendedCrops: RecommendedCrop[];
  improvements: string[];
  costBenefitAnalysis: CostBenefit;
  bestPractices: string[];
  expectedDiseases: string[];
  videoTutorials: VideoTutorial[];
}

export interface RecommendedCrop {
  name: string;
  variety: string;
  plantingTime: string;
  harvestTime: string;
  expectedYield: string;
  qualityGrade: 'premium' | 'standard' | 'low';
  marketValue: number;
  profitability: 'high' | 'medium' | 'low';
}

export interface CostBenefit {
  initialInvestment: number;
  expectedRevenue: number;
  profitMargin: number;
  breakEvenTime: string;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface VideoTutorial {
  title: string;
  url: string;
  duration: string;
  description: string;
  relevanceScore: number;
}

export interface Country {
  name: string;
  code: string;
  provinces: Province[];
}

export interface Province {
  name: string;
  code: string;
}

export interface ValueAdditionTip {
  method: string;
  description: string;
  costEstimate: string;
  profitIncrease: string;
  difficulty: 'easy' | 'medium' | 'hard';
  videoTutorials: VideoTutorial[];
  equipment: string[];
}

export interface PreservationMethod {
  method: string;
  description: string;
  shelfLifeExtension: string;
  costEstimate: string;
  difficulty: 'easy' | 'medium' | 'hard';
  videoTutorials: VideoTutorial[];
  equipment: string[];
}

export interface InputSource {
  name: string;
  type: 'seeds' | 'fertilizer' | 'pesticide' | 'equipment' | 'feed';
  supplier: string;
  price: number;
  quality: 'premium' | 'standard' | 'budget';
  location: string;
  contact: string;
  rating: number;
  availability: 'in-stock' | 'limited' | 'out-of-stock';
}