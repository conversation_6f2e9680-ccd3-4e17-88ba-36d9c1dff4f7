import React from 'react';
import { motion } from 'framer-motion';
import { Activity, Camera, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';

const RecentActivity: React.FC = () => {
  const activities = [
    {
      id: 1,
      type: 'analysis',
      title: 'Disease Analysis Completed',
      description: 'Tomato leaf blight detected',
      time: '2 hours ago',
      icon: Camera,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10'
    },
    {
      id: 2,
      type: 'market',
      title: 'Price Alert',
      description: 'Maize prices increased by 15%',
      time: '4 hours ago',
      icon: TrendingUp,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10'
    },
    {
      id: 3,
      type: 'warning',
      title: 'Weather Alert',
      description: 'Heavy rainfall expected',
      time: '6 hours ago',
      icon: AlertTriangle,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/10'
    },
    {
      id: 4,
      type: 'success',
      title: 'Treatment Applied',
      description: 'Fertilizer applied to beans',
      time: '1 day ago',
      icon: CheckCircle,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10'
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-navy-900/50 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
    >
      <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
        <Activity className="w-5 h-5 text-primary-400" />
        Recent Activity
      </h3>

      <div className="space-y-4">
        {activities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            className="group cursor-pointer"
          >
            <div className="flex items-start gap-3 p-3 rounded-xl hover:bg-white/5 transition-colors duration-200">
              <div className={`p-2 rounded-lg ${activity.bgColor} flex-shrink-0`}>
                <activity.icon className={`w-4 h-4 ${activity.color}`} />
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="text-white font-medium text-sm mb-1">
                  {activity.title}
                </h4>
                <p className="text-gray-400 text-xs mb-2">
                  {activity.description}
                </p>
                <span className="text-gray-500 text-xs">
                  {activity.time}
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full mt-4 py-2 text-primary-400 hover:text-primary-300 text-sm font-medium border border-primary-500/20 rounded-xl hover:bg-primary-500/10 transition-colors duration-200"
      >
        View All Activities
      </motion.button>
    </motion.div>
  );
};

export default RecentActivity;