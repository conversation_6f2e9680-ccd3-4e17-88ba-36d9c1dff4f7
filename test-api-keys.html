<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zeus Agri App - API Key Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .working { border-color: #4CAF50; background: #1e3a1e; }
        .failed { border-color: #f44336; background: #3a1e1e; }
        .error { border-color: #ff9800; background: #3a2e1e; }
        .testing { border-color: #2196F3; background: #1e2a3a; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .key-preview { font-family: monospace; font-size: 12px; opacity: 0.7; }
    </style>
</head>
<body>
    <h1>🔧 Zeus Agri App - API Key Health Check</h1>
    <p>Testing all your OpenRouter API keys for optimal performance...</p>
    
    <button onclick="testAllKeys()" id="testBtn">🚀 Test All API Keys</button>
    
    <div id="results"></div>

    <script>
        const API_KEYS = {
            'DEEPSEEK_R1_PRIMARY': 'sk-or-v1-0157614e7e2d12d1cc5ae51e292e28a5aa45ec3aa790664988e508f3efdbedb',
            'DEEPSEEK_R1_CHIMERA': '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',
            'GEMMA_3B': '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',
            'ZEUS_SHOP_SENTRY': 'sk-or-v1-****************************************************************'
        };

        async function testApiKey(keyName, apiKey) {
            try {
                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: 'deepseek/deepseek-r1',
                        messages: [{ role: 'user', content: 'Test agricultural analysis capability' }],
                        max_tokens: 50
                    })
                });

                const result = await response.json();
                
                return {
                    status: response.ok ? 'WORKING' : 'FAILED',
                    statusCode: response.status,
                    keyPreview: `${apiKey.substring(0, 25)}...`,
                    response: result.choices?.[0]?.message?.content || result.error?.message || 'No response'
                };
            } catch (error) {
                return {
                    status: 'ERROR',
                    error: error.message,
                    keyPreview: `${apiKey.substring(0, 25)}...`
                };
            }
        }

        async function testAllKeys() {
            const testBtn = document.getElementById('testBtn');
            const resultsDiv = document.getElementById('results');
            
            testBtn.disabled = true;
            testBtn.textContent = '🔄 Testing...';
            resultsDiv.innerHTML = '';

            for (const [keyName, apiKey] of Object.entries(API_KEYS)) {
                // Show testing status
                const testingDiv = document.createElement('div');
                testingDiv.className = 'test-result testing';
                testingDiv.innerHTML = `
                    <h3>🔄 ${keyName}</h3>
                    <div class="key-preview">${apiKey.substring(0, 25)}...</div>
                    <p>Testing...</p>
                `;
                resultsDiv.appendChild(testingDiv);

                // Test the key
                const result = await testApiKey(keyName, apiKey);
                
                // Update with results
                testingDiv.className = `test-result ${result.status.toLowerCase()}`;
                testingDiv.innerHTML = `
                    <h3>${result.status === 'WORKING' ? '✅' : result.status === 'FAILED' ? '❌' : '⚠️'} ${keyName}</h3>
                    <div class="key-preview">${result.keyPreview}</div>
                    <p><strong>Status:</strong> ${result.status}</p>
                    ${result.statusCode ? `<p><strong>HTTP Status:</strong> ${result.statusCode}</p>` : ''}
                    ${result.response ? `<p><strong>Response:</strong> ${result.response.substring(0, 100)}...</p>` : ''}
                    ${result.error ? `<p><strong>Error:</strong> ${result.error}</p>` : ''}
                `;
            }

            testBtn.disabled = false;
            testBtn.textContent = '🔄 Test Again';
        }

        // Auto-test on page load
        window.onload = () => {
            setTimeout(testAllKeys, 1000);
        };
    </script>
</body>
</html>
