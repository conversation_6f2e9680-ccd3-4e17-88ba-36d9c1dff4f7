// Test emergency detection functionality
import { webCrawlingService } from '../services/webCrawlingService';

export async function testEmergencyDetection() {
  console.log('🧪 Testing Emergency Detection System...');
  
  // Test case 1: Critical livestock emergency
  const emergencySymptoms = 'they are just sleeping continuously, sick, not aware and active';
  const animalType = 'Cattle';
  const location = 'Midlands, Zimbabwe';
  
  try {
    const emergencyAdvice = await webCrawlingService.getLivestockEmergencyAdvice(
      animalType, 
      emergencySymptoms, 
      location
    );
    
    console.log('Emergency Detection Results:');
    console.log('Urgency Level:', emergencyAdvice?.urgencyLevel);
    console.log('Immediate Actions:', emergencyAdvice?.immediateActions);
    
    return emergencyAdvice;
  } catch (error) {
    console.error('Emergency detection test failed:', error);
    return null;
  }
}

// Test the urgency assessment
export function testUrgencyAssessment() {
  const testCases = [
    {
      symptoms: 'cow is down and unable to stand',
      expected: 'CRITICAL'
    },
    {
      symptoms: 'they are just sleeping continuously, sick, not aware and active',
      expected: 'CRITICAL'
    },
    {
      symptoms: 'not eating well, seems tired',
      expected: 'HIGH'
    },
    {
      symptoms: 'slight cough, otherwise normal',
      expected: 'MODERATE'
    }
  ];
  
  console.log('🧪 Testing Urgency Assessment...');
  
  testCases.forEach((testCase, index) => {
    // Simulate the urgency assessment logic
    const criticalSymptoms = [
      'down', 'unable to stand', 'not moving', 'unconscious',
      'bloated', 'difficulty breathing', 'seizure', 'bleeding',
      'sleeping continuously', 'not aware', 'not active'
    ];
    
    const highSymptoms = [
      'not eating', 'fever', 'diarrhea', 'vomiting', 'limping severely'
    ];

    const lowerSymptoms = testCase.symptoms.toLowerCase();
    
    let result;
    if (criticalSymptoms.some(symptom => lowerSymptoms.includes(symptom))) {
      result = 'CRITICAL';
    } else if (highSymptoms.some(symptom => lowerSymptoms.includes(symptom))) {
      result = 'HIGH';
    } else {
      result = 'MODERATE';
    }
    
    const passed = result.includes(testCase.expected);
    console.log(`Test ${index + 1}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Symptoms: "${testCase.symptoms}"`);
    console.log(`  Expected: ${testCase.expected}, Got: ${result}`);
  });
}

// Run tests
if (typeof window === 'undefined') {
  // Node.js environment
  testUrgencyAssessment();
  testEmergencyDetection().then(result => {
    console.log('Emergency detection test completed:', result ? '✅ Success' : '❌ Failed');
  });
}
