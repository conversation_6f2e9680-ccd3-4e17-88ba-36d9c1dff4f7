#!/usr/bin/env npx tsx

import { aiService } from '../services/aiService';
import { webCrawlingService } from '../services/webCrawlingService';

console.log('🧪 TESTING REAL-TIME DATA FLOW TO FRONTEND');
console.log('='.repeat(80));

async function testRealTimeDataFlow() {
  try {
    console.log('🔍 Testing Market Analysis Data Flow...');
    
    // Test market analysis
    const marketData = await webCrawlingService.getRealTimeMarketAnalysis('tomato', 'Midlands, Zimbabwe');
    console.log('💰 MARKET DATA STRUCTURE:');
    console.log(JSON.stringify(marketData, null, 2));
    
    console.log('\n🔍 Testing Value Addition Data Flow...');
    
    // Test value addition
    const valueAddition = await webCrawlingService.getValueAdditionRecommendations('tomato', 'Midlands, Zimbabwe');
    console.log('💼 VALUE ADDITION DATA STRUCTURE:');
    console.log(JSON.stringify(valueAddition, null, 2));
    
    console.log('\n🔍 Testing YouTube Video Links...');
    
    // Test YouTube videos
    const youtubeVideos = await webCrawlingService.getRealYouTubeVideoLinks('tomato', 'bacterial wilt', 'disease');
    console.log('📹 YOUTUBE VIDEO DATA STRUCTURE:');
    console.log(JSON.stringify(youtubeVideos, null, 2));
    
    console.log('\n🧠 Testing Complete AI Analysis Flow...');
    
    // Test complete AI analysis
    const fullAnalysis = await aiService.performExtremeIntelligentAnalysis(
      'crop',
      'tomato',
      'bacterial wilt, brown spots, wilting leaves',
      'Midlands, Zimbabwe',
      null
    );
    
    console.log('🎯 FULL ANALYSIS STRUCTURE:');
    console.log('📊 Diagnosis:', fullAnalysis.diagnosis);
    console.log('📈 Confidence:', fullAnalysis.confidence);
    console.log('💰 Market Data Present:', !!fullAnalysis.marketData);
    console.log('💼 Value Addition Present:', !!fullAnalysis.valueAddition);
    console.log('📹 Video Tutorials Present:', !!fullAnalysis.videoTutorials);
    
    if (fullAnalysis.marketData) {
      console.log('\n💰 MARKET DATA IN ANALYSIS:');
      console.log(JSON.stringify(fullAnalysis.marketData, null, 2));
    }
    
    if (fullAnalysis.valueAddition) {
      console.log('\n💼 VALUE ADDITION IN ANALYSIS:');
      console.log(JSON.stringify(fullAnalysis.valueAddition, null, 2));
    }
    
    if (fullAnalysis.videoTutorials && fullAnalysis.videoTutorials.length > 0) {
      console.log('\n📹 VIDEO TUTORIALS IN ANALYSIS:');
      fullAnalysis.videoTutorials.slice(0, 3).forEach((video: any, index: number) => {
        console.log(`${index + 1}. ${video.title}`);
        console.log(`   URL: ${video.url}`);
        console.log(`   Duration: ${video.duration}`);
        console.log(`   Relevance: ${video.relevanceScore || video.relevance}%`);
      });
    }
    
    console.log('\n✅ REAL-TIME DATA FLOW TEST COMPLETED SUCCESSFULLY!');
    console.log('🎯 All data structures are properly formatted for frontend display');
    
  } catch (error) {
    console.error('❌ Real-time data flow test failed:', error);
  }
}

testRealTimeDataFlow();
