import React from 'react';
import { motion } from 'framer-motion';
import { Menu, <PERSON>, Search, Cloud, Sun, X } from 'lucide-react';

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar, sidebarOpen }) => {
  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className="h-16 bg-navy-900/80 backdrop-blur-xl border-b border-primary-500/20 flex items-center justify-between px-4 sm:px-6 flex-shrink-0 relative z-30"
    >
      <div className="flex items-center gap-4">
        {/* Mobile/Tablet Menu Button - Always visible on smaller screens */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={toggleSidebar}
          className="lg:hidden p-2 rounded-lg bg-primary-500/10 text-primary-400 hover:bg-primary-500/20 transition-colors border border-primary-500/20"
        >
          {sidebarOpen ? (
            <X className="w-5 h-5" />
          ) : (
            <Menu className="w-5 h-5" />
          )}
        </motion.button>
        
        {/* Search Bar */}
        <div className="hidden md:flex items-center gap-2 bg-white/5 backdrop-blur-md rounded-xl px-4 py-2 border border-white/10 min-w-0">
          <Search className="w-4 h-4 text-gray-400 flex-shrink-0" />
          <input
            type="text"
            placeholder="Search crops, diseases, market data..."
            className="bg-transparent text-white placeholder-gray-400 outline-none w-full min-w-0 text-sm"
          />
        </div>

        {/* Mobile Search Button */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="md:hidden p-2 rounded-lg bg-white/5 text-gray-400 hover:text-white hover:bg-white/10 transition-colors"
        >
          <Search className="w-5 h-5" />
        </motion.button>
      </div>

      <div className="flex items-center gap-2 sm:gap-4">
        {/* Weather Widget */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="hidden sm:flex items-center gap-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-md rounded-xl px-3 py-2 border border-white/10"
        >
          <Sun className="w-4 h-4 text-yellow-400" />
          <span className="text-white text-sm">24°C</span>
          <Cloud className="w-4 h-4 text-blue-400" />
        </motion.div>

        {/* Notifications */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="relative p-2 rounded-lg bg-primary-500/10 text-primary-400 hover:bg-primary-500/20 transition-colors border border-primary-500/20"
        >
          <Bell className="w-5 h-5" />
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center text-xs text-white">
            3
          </span>
        </motion.button>
      </div>
    </motion.header>
  );
};

export default Header;