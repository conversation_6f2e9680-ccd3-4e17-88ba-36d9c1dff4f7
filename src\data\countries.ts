export const countries = [
  {
    name: "Zimbabwe",
    code: "ZW",
    provinces: [
      { name: "Harare", code: "HA" },
      { name: "Bulawayo", code: "BU" },
      { name: "Manicaland", code: "MA" },
      { name: "Mashonaland Central", code: "MC" },
      { name: "Mashonaland East", code: "ME" },
      { name: "Mashonaland West", code: "MW" },
      { name: "Masvingo", code: "MV" },
      { name: "Matabeleland North", code: "MN" },
      { name: "Matabeleland South", code: "MS" },
      { name: "Midlands", code: "MI" }
    ]
  },
  {
    name: "South Africa",
    code: "ZA",
    provinces: [
      { name: "Eastern Cape", code: "EC" },
      { name: "Free State", code: "FS" },
      { name: "Gauteng", code: "GP" },
      { name: "KwaZulu-Natal", code: "KZN" },
      { name: "Limpopo", code: "LP" },
      { name: "Mpumalanga", code: "MP" },
      { name: "Northern Cape", code: "NC" },
      { name: "North West", code: "NW" },
      { name: "Western Cape", code: "WC" }
    ]
  },
  {
    name: "Kenya",
    code: "KE",
    provinces: [
      { name: "Nairobi", code: "NRB" },
      { name: "Central", code: "CEN" },
      { name: "Coast", code: "CST" },
      { name: "Eastern", code: "EST" },
      { name: "North Eastern", code: "NET" },
      { name: "Nyanza", code: "NYZ" },
      { name: "Rift Valley", code: "RVL" },
      { name: "Western", code: "WST" }
    ]
  },
  {
    name: "Nigeria",
    code: "NG",
    provinces: [
      { name: "Lagos", code: "LA" },
      { name: "Kano", code: "KN" },
      { name: "Kaduna", code: "KD" },
      { name: "Oyo", code: "OY" },
      { name: "Rivers", code: "RI" },
      { name: "Ogun", code: "OG" },
      { name: "Imo", code: "IM" },
      { name: "Delta", code: "DE" },
      { name: "Sokoto", code: "SO" },
      { name: "Ogun", code: "OG" }
    ]
  },
  {
    name: "Ghana",
    code: "GH",
    provinces: [
      { name: "Greater Accra", code: "AA" },
      { name: "Ashanti", code: "AH" },
      { name: "Western", code: "WP" },
      { name: "Central", code: "CP" },
      { name: "Eastern", code: "EP" },
      { name: "Volta", code: "TV" },
      { name: "Northern", code: "NP" },
      { name: "Upper East", code: "UE" },
      { name: "Upper West", code: "UW" },
      { name: "Brong Ahafo", code: "BA" }
    ]
  },
  {
    name: "United States",
    code: "US",
    provinces: [
      { name: "California", code: "CA" },
      { name: "Texas", code: "TX" },
      { name: "Florida", code: "FL" },
      { name: "New York", code: "NY" },
      { name: "Pennsylvania", code: "PA" },
      { name: "Illinois", code: "IL" },
      { name: "Ohio", code: "OH" },
      { name: "Georgia", code: "GA" },
      { name: "North Carolina", code: "NC" },
      { name: "Michigan", code: "MI" }
    ]
  },
  {
    name: "India",
    code: "IN",
    provinces: [
      { name: "Maharashtra", code: "MH" },
      { name: "Tamil Nadu", code: "TN" },
      { name: "Karnataka", code: "KA" },
      { name: "Gujarat", code: "GJ" },
      { name: "Rajasthan", code: "RJ" },
      { name: "West Bengal", code: "WB" },
      { name: "Madhya Pradesh", code: "MP" },
      { name: "Uttar Pradesh", code: "UP" },
      { name: "Delhi", code: "DL" },
      { name: "Punjab", code: "PB" }
    ]
  },
  {
    name: "Brazil",
    code: "BR",
    provinces: [
      { name: "São Paulo", code: "SP" },
      { name: "Rio de Janeiro", code: "RJ" },
      { name: "Minas Gerais", code: "MG" },
      { name: "Bahia", code: "BA" },
      { name: "Paraná", code: "PR" },
      { name: "Rio Grande do Sul", code: "RS" },
      { name: "Pernambuco", code: "PE" },
      { name: "Ceará", code: "CE" },
      { name: "Pará", code: "PA" },
      { name: "Santa Catarina", code: "SC" }
    ]
  },
  {
    name: "Australia",
    code: "AU",
    provinces: [
      { name: "New South Wales", code: "NSW" },
      { name: "Victoria", code: "VIC" },
      { name: "Queensland", code: "QLD" },
      { name: "Western Australia", code: "WA" },
      { name: "South Australia", code: "SA" },
      { name: "Tasmania", code: "TAS" },
      { name: "Australian Capital Territory", code: "ACT" },
      { name: "Northern Territory", code: "NT" }
    ]
  },
  {
    name: "Canada",
    code: "CA",
    provinces: [
      { name: "Ontario", code: "ON" },
      { name: "Quebec", code: "QC" },
      { name: "British Columbia", code: "BC" },
      { name: "Alberta", code: "AB" },
      { name: "Manitoba", code: "MB" },
      { name: "Saskatchewan", code: "SK" },
      { name: "Nova Scotia", code: "NS" },
      { name: "New Brunswick", code: "NB" },
      { name: "Newfoundland and Labrador", code: "NL" },
      { name: "Prince Edward Island", code: "PE" }
    ]
  }
];

export const getCountryByCode = (code: string) => {
  return countries.find(country => country.code === code);
};

export const getProvincesByCountry = (countryCode: string) => {
  const country = getCountryByCode(countryCode);
  return country ? country.provinces : [];
};